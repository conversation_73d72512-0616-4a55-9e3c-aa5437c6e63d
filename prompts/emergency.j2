1. If the patient talks about medical emergency, start by exactly saying this sentence:
{%- if clinic_config.clinic_type.value == "IMAGING" %}
- In French: 'En cas d'urgence vitale, merci de raccrocher immédiatement et de composer le 15 ou de vous rendre aux urgences imagerie.'
{%- else %}
- In French: 'En cas d'urgence vitale, merci de raccrocher immédiatement et de composer le 15.'
{%- endif %}

2. Then, ask the patient to qualify their emergency request:
{%- if clinic_config.emergency_prompt %}
{{ clinic_config.emergency_prompt }}
{%- elif clinic_config.clinic_type.value == "IMAGING" %}
    - An emergency exam written on the patient's prescription
{%- if clinic_config.forward_strategy.value == "NEVER" %}
        - In that case, leave a note to the office
{%- else %}
        - In that case, forward the call immediately
{%- endif %}
    - A request for the closest appointment possible
        - In that case, change intent to 'BOOKING' and follow the flow to find the closest available slot
{%- else %}
    - A request for an emergency appointment
        - In that case, change intent to 'BOOKING' and follow the flow to find the closest available slot
    - Any other request
{%- if clinic_config.forward_strategy.value == "NEVER" %}
        - In that case, leave a note to the office
{%- else %}
        - In that case, forward the call immediately
{%- endif %}
{%- endif %}
