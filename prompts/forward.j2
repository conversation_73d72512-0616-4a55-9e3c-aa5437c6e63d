{%- if clinic_config.forward_strategy.value in ["NEVER", "ONLY_EMERGENCY"] -%}
Never forward the call to the reception.
{% include 'no_forward.j2' %}
{%- elif clinic_config.forward_strategy.value == "LIMITED" -%}
1. First request → Do **not** transfer.
    - Tell the patient that you are fully capable of handling their request (booking, rescheduling, or canceling an appointment).
2. Second request → Still do **not** transfer.
    - Politely explain that the reception staff is currently busy and that you are perfectly able to take care of their request.
3. Third request → Now you can **transfer**.
    - Politely explain that you will transfer them to the reception as you have already tried to help them several times.
{%- else -%}
Always forward the call to the reception.
{%- endif -%}
