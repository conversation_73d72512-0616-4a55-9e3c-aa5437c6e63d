import asyncio
import json
import os

import sentry_sdk
from azure.cognitiveservices.speech import ProfanityOption  # type: ignore
from dotenv import load_dotenv
from livekit.agents import JobContext, JobProcess, WorkerOptions, cli, metrics
from livekit.agents.voice import AgentSession
from livekit.agents.voice.room_io import RoomInputOptions
from livekit.plugins import (
    azure,
    cartesia,
    elevenlabs,
    google,
    noise_cancellation,
    openai,
    silero
)
from livekit.plugins.azure.tts import ProsodyConfig
from livekit.plugins.elevenlabs import VoiceSettings
from loguru import logger

from agent.base_agent import BaseAgent
from agent.phone_collector_agent import PhoneCollectorAgent
from agent.simple_agent import SimpleAgent
from agent.welcome_agent import WelcomeAgent
from models.user_data import UserData
from services.user_data import prepare_user_data
from utils.audio import load_audio_file
from utils.config import get_call_config
from utils.latency_logger import LatencyLogger
from utils.livekit_config import (
    handle_connection_quality_change,
    handle_conversation_item_added,
    handle_error,
    handle_log_usage,
    handle_metrics_collected,
    handle_session_end,
    handle_user_away
)
from utils.logger import configure_logger
from utils.phone_numbers import is_valid_french_mobile_phone_number

load_dotenv()

SENTRY_DSN = os.getenv("SENTRY_DSN")
AGENT_NAME = os.getenv("AGENT_NAME", "")
IS_DEV = not AGENT_NAME

if not IS_DEV:
    sentry_sdk.init(
        dsn=SENTRY_DSN,
        # Add data like request headers and IP for users,
        # see https://docs.sentry.io/platforms/python/data-management/data-collected/ for more info
        send_default_pii=True,
        # Set traces_sample_rate to 1.0 to capture 100%
        # of transactions for tracing.
        traces_sample_rate=1.0,
        environment="production" if not IS_DEV else "development",
    )


def prewarm(proc: JobProcess) -> None:
    logger.info("Warming up")
    proc.userdata["vad"] = silero.VAD.load(
        min_speech_duration=0.1,
        min_silence_duration=1,
        prefix_padding_duration=0.5,
        max_buffered_speech=15,
        activation_threshold=0.6,
    )


async def entrypoint(ctx: JobContext) -> None:
    logger.info("Taking call")

    call_config = await get_call_config(IS_DEV, ctx)
    configure_logger(call_config)

    userdata = await prepare_user_data(call_config, ctx)

    if userdata.clinic_config.is_demo:
        call_config.voice_speed = 1.2

    tts = (
        elevenlabs.TTS(
            voice_id="SfFCqCe4hBx8HN22DzaR",
            model="eleven_turbo_v2_5",
            voice_settings=VoiceSettings(
                stability=0.75, similarity_boost=0.75, speed=1.07
            ),  # TODO: Make speed configurable
            language="fr",
        )
        if "11labs-tts" in userdata.clinic_config.experiments
        else (
            google.TTS(
                language="fr-FR",
                gender="female",
                # speaking_rate=call_config.voice_speed,
                voice_name="fr-FR-Chirp3-HD-Achernar",
                credentials_info=json.loads(os.getenv("GOOGLE_TTS_CREDENTIALS", "")),
            )
            if "google-tts" in userdata.clinic_config.experiments
            else (
                cartesia.TTS(
                    language="fr",
                    voice=os.getenv("CARTESIA_VOICE_ID", ""),
                    speed=call_config.voice_speed - 1.3,
                )
                if "cartesia-tts" in userdata.clinic_config.experiments
                else azure.TTS(
                    voice="fr-FR-DeniseNeural",
                    prosody=ProsodyConfig(rate=call_config.voice_speed),
                )
            )
        )
    )

    session = AgentSession[UserData](
        userdata=userdata,
        stt=azure.STT(
            language="fr-FR",
            segmentation_silence_timeout_ms=700,
            phrase_list=userdata.clinic_config.words_boost,
            profanity=ProfanityOption.Raw,
        ),
        llm=openai.LLM.with_azure(temperature=0),
        tts=tts,
        vad=ctx.proc.userdata["vad"],
        turn_detection="vad",
        max_tool_steps=3,
        user_away_timeout=15,
        min_endpointing_delay=1,
        max_endpointing_delay=2,
        min_interruption_duration=0.7,
        min_interruption_words=2,
        preemptive_generation=True,
        allow_interruptions=call_config.is_dev,
        discard_audio_if_uninterruptible=False,
        # to use realtime model, replace the stt, llm, tts and vad with the following
        # llm=openai.realtime.RealtimeModel(voice="alloy"),
    )

    # Determine start agent
    # - Default: WelcomeAgent
    # - If clinic doesn't have a booking provider: SimpleAgent
    # - If caller phone number is valid french mobile number: PhoneCollectorAgent
    # TODO: we need to split BaseAgent into 2:
    #  1. BaseAgent: implements mute_on_enter and global system prompt without tool instructions
    #  2. FlowAgent (name TBD): adds tools & tools instructions
    start_agent: BaseAgent = WelcomeAgent(userdata.clinic_config)
    if not userdata.clinic_config.booking_provider_type:
        start_agent = SimpleAgent(userdata.clinic_config)
        # TODO: should be located in SimpleAgent directly
        await start_agent.update_tools([start_agent.forward_call, start_agent.end_call])
    elif not is_valid_french_mobile_phone_number(call_config.caller_phone_number):
        start_agent = PhoneCollectorAgent(userdata.clinic_config)

    start_agent.mute_on_enter = (
        bool(userdata.clinic_config.pre_roll_audio_url) and not call_config.is_dev
    )

    # Start session
    await session.start(
        agent=start_agent,
        room=ctx.room,
        room_input_options=RoomInputOptions(
            pre_connect_audio=True,
            video_enabled=False,
            noise_cancellation=(
                noise_cancellation.BVC()
                if call_config.is_dev
                else noise_cancellation.BVCTelephony()
            ),
        ),
    )

    # Launch pre-roll audio if configured

    if userdata.clinic_config.pre_roll_audio_url and not call_config.is_dev:
        logger.info("Loading pre-roll audio")
        audio_frames = load_audio_file(userdata.clinic_config.pre_roll_audio_url)
        await session.say(".", audio=audio_frames, allow_interruptions=False)
        await session.generate_reply()
        await asyncio.sleep(3)

    # Start background audio player
    await userdata.audio_player.start(room=userdata.ctx.room, agent_session=session)

    # Events & metrics handlers
    latency_logger = LatencyLogger()
    usage_collector = metrics.UsageCollector()

    session.on("user_state_changed", handle_user_away(session))
    session.on("conversation_item_added", handle_conversation_item_added)
    session.on(
        "metrics_collected", handle_metrics_collected(usage_collector, latency_logger)
    )
    session.on("error", handle_error(session))
    ctx.add_shutdown_callback(handle_log_usage(usage_collector))
    ctx.room.on("connection_quality_changed", handle_connection_quality_change(session))
    ctx.room.on("participant_disconnected", handle_session_end(session))


if __name__ == "__main__":
    logger.info(f"Starting agent {AGENT_NAME}")
    cli.run_app(
        WorkerOptions(
            prewarm_fnc=prewarm,
            entrypoint_fnc=entrypoint,
            agent_name=AGENT_NAME,
            drain_timeout=12000,
        )
    )
