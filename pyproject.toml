[project]
name = "livekit-poc"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"

dependencies = [
    "jinja2>=3.1.6",
    "livekit-agents[azure,cartesia,deepgram,elevenlabs,google,openai,silero,turn-detector]==1.2.8",
    "livekit-plugins-noise-cancellation>=0.2.5",
    "loguru>=0.7.3",
    "phonenumbers>=9.0.14",
    "python-dateutil>=2.9.0.post0",
    "python-dotenv>=1.1.1",
    "sentry-sdk>=2.34.1",
    "supabase>=2.17.0",
    "twilio>=9.7.0",
]

[dependency-groups]
dev = [
    "black>=25.1.0",
    "isort>=6.0.1",
    "mypy>=1.17.0",
    "pydantic>=2.11.7",
    "pytest>=8.4.2",
    "ruff>=0.12.4",
    "twilio-stubs>=0.2.0",
    "types-python-dateutil>=2.9.0.20250822",
    "types-requests>=2.32.4.20250611",
]

[tool.isort]
multi_line_output = 3 # For one-import-per-line: https://pycqa.github.io/isort/docs/configuration/options.html#multi-line-output
