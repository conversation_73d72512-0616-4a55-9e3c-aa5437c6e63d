from datetime import datetime

from livekit.agents import ChatContext, ChatMessage
from loguru import logger

from lib.n8n_client import n8n_client
from models.user_data import CallIntent, UserData
from services.telephony import fetch_twilio_recording_url

INTENT_TO_TAG = {
    CallIntent.BOOKING: "Nouveau RDV",
    CallIntent.RESCHEDULE: "Modification RDV",
    CallIntent.CANCEL: "Annulation RDV",
    CallIntent.DELAY: "Retard",
    CallIntent.CONFIRM: "Confirmation RDV",
    CallIntent.QUESTIONS: "Question/Tâche médicale",
}

# TODO: this is temporary to match Pipecat's enums
INTENT_TO_OLD_INTENTS = {
    CallIntent.BOOKING: "nouveau",
    CallIntent.RESCHEDULE: "modifier",
    CallIntent.CANCEL: "annuler",
    CallIntent.DELAY: "retard",
    CallIntent.CONFIRM: "confirmer",
    CallIntent.QUESTIONS: "question",
}


async def report_call(userdata: UserData, chat_ctx: ChatContext) -> None:
    if userdata.call_config.is_dev:
        return

    recording_url = await fetch_twilio_recording_url(userdata.call_config.call_id)
    conversation_transcript = "\n".join(
        [
            f"{item.role.capitalize()}: {item.content[0]}"
            for item in chat_ctx.items
            if isinstance(item, ChatMessage)
        ]
    )

    report_call_body = {
        "config": userdata.config_id,
        "call_duration": str(datetime.now() - userdata.call_config.start_time),
        "phone_caller": {
            "id": userdata.call_config.call_id,
            "phone_number": userdata.call_config.caller_phone_number,
            "recording_url": recording_url,
            "conversation_transcript": conversation_transcript,
            "intent": INTENT_TO_OLD_INTENTS.get(userdata.intent, "start"),
            "has_note": len(userdata.notes) > 0,
            # "room_url": "",
            "has_forward_call": userdata.is_call_forwarded,
            "successful_call": userdata.is_call_successful,
            "is_demo": userdata.clinic_config.is_demo,
        },
        "call_type": INTENT_TO_TAG.get(userdata.intent, "None"),
        "experiments": userdata.clinic_config.experiments,
        "bot_version": 3,
    }

    if userdata.patient:
        treating_doctor = userdata.patient.treating_doctor
        report_call_body.update(
            {
                "patient_data": {
                    "id": userdata.patient.id,
                    "is_new_patient": False,
                    "first_name": userdata.patient.first_name,
                    "last_name": userdata.patient.last_name,
                    "birthdate": userdata.patient.birthdate.strftime("%Y-%m-%d"),
                    "email": userdata.patient.email,
                    "phone_number": userdata.call_config.caller_phone_number,
                    "medecin_historique": (
                        treating_doctor.model_dump(mode="json")
                        if treating_doctor
                        else None
                    ),
                },
            }
        )
    elif userdata.patient_identity:
        report_call_body.update(
            {
                "patient_data": {
                    "id": userdata.patient_identity.id,
                    "is_new_patient": not userdata.patient_identity.id,
                    "first_name": userdata.patient_identity.first_name,
                    "last_name": userdata.patient_identity.last_name,
                    "birthdate": userdata.patient_identity.birthdate.strftime(
                        "%Y-%m-%d"
                    ),
                    "phone_number": userdata.call_config.caller_phone_number,
                },
            }
        )
    elif len(userdata.retrieved_patients) > 0:
        # When the patient hasn't identified themselves but we have retrieved patients, we take the first one to fill the call report
        supposed_patient = userdata.retrieved_patients[0]
        report_call_body.update(
            {
                "patient_data": {
                    "id": supposed_patient.id,
                    "is_new_patient": False,
                    "first_name": supposed_patient.first_name,
                    "last_name": supposed_patient.last_name,
                    "birthdate": supposed_patient.birthdate.strftime("%Y-%m-%d"),
                    "email": supposed_patient.email,
                    "phone_number": userdata.call_config.caller_phone_number,
                },
            }
        )

    if userdata.appointment:
        practitioner_id = next(
            (
                a.practitioner_id
                for a in userdata.clinic_config.agendas
                if a.id == userdata.appointment.agenda_id
            ),
            None,
        )
        report_call_body.update(
            {
                "appointment_data": {
                    "id": userdata.appointment.id,
                    "patient_id": userdata.appointment.patient_id,
                    "visit_motive_id": userdata.appointment.motive.id,
                    "visit_motive_name": userdata.appointment.motive.name,
                    "agenda_id": userdata.appointment.agenda_id,
                    "practitioner_id": practitioner_id,
                    "is_booking_created": True,
                },
            }
        )

    if userdata.call_config.is_dev:
        logger.info("Not reporting call in dev mode")
        logger.info(f"Report call body: {report_call_body}")
        return

    await n8n_client.post(
        url="f71367a7-97aa-4dd9-a799-666f4c97cd93",
        json=report_call_body,
    )
