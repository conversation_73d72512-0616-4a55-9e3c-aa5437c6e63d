from livekit.agents import JobContext

from agent.booking_agent import BookingAgent
from agent.cancel_agent import CancelAgent
from agent.confirm_agent import ConfirmAgent
from agent.delay_agent import DelayAgent
from agent.questions_agent import QuestionsAgent
from agent.reschedule_agent import RescheduleAgent
from models.calls import CallConfig
from models.user_data import UserData
from services.clinics import get_clinc_config, get_company_clinics
from services.patients import (
    RetrievePatientRequest,
    retrieve_existing_patients
)
from utils.audio import setup_background_audio


async def prepare_user_data(call_config: CallConfig, job_ctx: JobContext) -> UserData:
    clinic_config = await get_clinc_config(call_config)

    retrieved_patients = await retrieve_existing_patients(
        clinic_config,
        RetrievePatientRequest(phone_number=call_config.caller_phone_number),
    )

    company_clinics = (
        get_company_clinics(clinic_config.config_name)
        if clinic_config.select_clinic
        else []
    )

    user_data = UserData(
        config_id=clinic_config.config_id,
        call_config=call_config,
        clinic_config=clinic_config,
        retrieved_patients=retrieved_patients,
        company_clinics=company_clinics,
        agents={
            "booking": BookingAgent(
                clinic_config=clinic_config, retrieved_patients=retrieved_patients
            ),
            "reschedule": RescheduleAgent(
                clinic_config=clinic_config, retrieved_patients=retrieved_patients
            ),
            "cancel": CancelAgent(
                clinic_config=clinic_config, retrieved_patients=retrieved_patients
            ),
            "delay": DelayAgent(
                clinic_config=clinic_config, retrieved_patients=retrieved_patients
            ),
            "questions": QuestionsAgent(
                clinic_config=clinic_config, retrieved_patients=retrieved_patients
            ),
            "confirm": ConfirmAgent(
                clinic_config=clinic_config, retrieved_patients=retrieved_patients
            ),
        },
        audio_player=setup_background_audio(),
        ctx=job_ctx,
    )

    if not user_data.clinic_config.booking_provider_type:
        user_data.agents = {
            k: v for k, v in user_data.agents.items() if k in ["welcome", "simple"]
        }

    return user_data
