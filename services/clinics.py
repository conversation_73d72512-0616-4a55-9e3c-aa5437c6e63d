import os

import sentry_sdk
from loguru import logger

from lib.supabase_client import supabase_client
from models.calls import CallConfig
from models.clinics import Agenda, ClinicConfig, DelayStrategy, ForwardStrategy
from models.motives import Motive
from models.specialities import Speciality
from utils.experiments import get_experiments
from utils.strings import remove_text_in_parentheses


async def get_clinc_config(call_config: CallConfig) -> ClinicConfig:
    knowledge_base_query = supabase_client.table("knowledge-bases").select("*")

    if call_config.is_dev:
        dev_config_id = os.getenv("DEV_CONFIG_ID")
        if not dev_config_id:
            raise ValueError("Missing dev config ID")
        knowledge_base_query = knowledge_base_query.eq("config", dev_config_id)
    else:
        knowledge_base_query = knowledge_base_query.eq(
            "twilio_inbound_call_phone_number", call_config.clinic_phone_number
        )

    knowledge_base_response = knowledge_base_query.execute()
    knowledge_base = (
        knowledge_base_response.data[0] if knowledge_base_response.data else None
    )

    if not knowledge_base:
        raise ValueError(f"No knowledge base found for config {call_config}")

    config_id = knowledge_base["config"]
    sentry_sdk.set_tag("config_id", config_id)
    clinic_config = get_clinic_config(config_id)
    clinic_config.experiments = get_experiments(config_id, call_config.is_dev)

    return clinic_config


# TODO: FIXME double call to Supabase
def get_clinic_config(config_id: str) -> ClinicConfig:
    knowledge_base_response = (
        supabase_client.table("knowledge-bases")
        .select("*")
        .eq("config", config_id)
        .execute()
    )
    knowledge_base = (
        knowledge_base_response.data[0] if knowledge_base_response.data else None
    )

    if not knowledge_base:
        raise ValueError(f"No knowledge base found for config {config_id}")

    bot_parameters_response = (
        supabase_client.table("bot_parameters")
        .select("*")
        .eq("config", config_id)
        .execute()
    )
    bot_parameters = (
        bot_parameters_response.data[0] if bot_parameters_response.data else {}
    )

    agendas = [
        Agenda(**agenda)
        for agenda in knowledge_base.get("inbound_config_file", {}).get("calendars", [])
    ]

    motives = [
        Motive(
            id=m.get("id"),
            name=m.get("name"),
            open=m.get("open"),
            instructions=m.get("instructions"),
            min_age=m.get("age_minimum"),
            max_age=m.get("age_maximum"),
            hint=m.get("prompt_hint"),
            should_transfer=m.get("should_transfer", False),
            practice_ids=m.get("practice_ids", []),
            speciality_id=m.get("speciality_id"),
        )
        for m in knowledge_base.get("inbound_config_file", {}).get(
            "visit-motives-categories", []
        )
    ]

    specialities = [
        Speciality(**s)
        for s in knowledge_base.get("inbound_config_file", {}).get("specialities", [])
    ]

    # TODO: ideally, we want single words:
    #  - words_boost_prompt should be a list of single words
    #  - doctor names should be split into first and last name
    #  - motive name should be split into single words and we should only keep technical wording (not "Consultation" for instance)
    #  - Full list should be unique
    words_boost = [
        meaning
        for _, meaning in (bot_parameters.get("words_boost_prompt") or {}).items()
    ]
    for motive in motives:
        words_boost.append(remove_text_in_parentheses(motive.name))
    for agenda in agendas:
        if not agenda.practitioner_id:  # Skip non-doctor agendas
            continue
        for word in agenda.name.split(" "):
            if len(word) < 3:  # Skip empty words
                continue
            words_boost.append(remove_text_in_parentheses(word))

    select_doctor = bot_parameters.get("ask_name_doctor", False)

    clinic_config = ClinicConfig(
        config_id=config_id,
        name=knowledge_base.get("name"),
        config_name=knowledge_base.get("config_name"),
        practice_id=knowledge_base.get("inbound_config_file", {}).get("practice_id"),
        clinic_type=knowledge_base.get("client_type"),
        booking_provider_type=knowledge_base.get("booking_provider"),
        address=knowledge_base.get("address"),
        openings=knowledge_base.get("openings_3"),
        additional_information=knowledge_base.get("additional_information"),
        motives=motives,
        agendas=agendas,
        specialities=specialities,
        pre_roll_audio_url=knowledge_base.get("audio_intro_url"),
        forward_number=knowledge_base.get("forward_number"),
        words_boost=words_boost,
        say_doctor_name=select_doctor or bot_parameters.get("say_name_doctor", False),
        select_doctor=select_doctor,
        select_clinic=bot_parameters.get("ask_clinic", False),
        emergency_prompt=knowledge_base.get("emergency_prompt"),
        motive_collection_prompt=knowledge_base.get("motive_collection_prompt"),
        confirmed_booking_instructions=bot_parameters.get("instructions_at_end"),
        delay_strategy=bot_parameters.get("delay_behavior", DelayStrategy.ADD_NOTE),
        forward_strategy=bot_parameters.get(
            "forward_strategy", ForwardStrategy.LIMITED
        ),
        min_age=bot_parameters.get("age_minimum"),
        max_age=bot_parameters.get("age_maximum"),
        is_demo=knowledge_base.get("is_demo", False),
    )

    logger.info(f"Found clinic config: {clinic_config.name}")
    logger.info(f"Active experiments: {clinic_config.experiments}")
    if clinic_config.is_demo:
        logger.warning(f"Using Demo config ID {clinic_config.config_id}")

    return clinic_config


def get_company_clinics(config_name: str) -> list[ClinicConfig]:
    clinics_response = (
        supabase_client.table("knowledge-bases")
        .select("config")
        .eq("config_name", config_name)
        .eq("is_active", True)
        .execute()
    )
    clinics_data = clinics_response.data if clinics_response.data else []

    return [
        get_clinic_config(clinic["config"])
        for clinic in clinics_data
        if clinic.get("config")
    ]
