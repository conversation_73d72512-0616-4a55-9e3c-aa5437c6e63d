from typing import List, Optional

from pydantic import BaseModel


class Motive(BaseModel):
    id: int
    name: str
    open: bool
    instructions: str
    hint: Optional[str] = None
    speciality_id: int

    practice_ids: List[str] = []
    min_age: Optional[float] = (
        None  # TODO: convert to int when config typing is neat (ex on config25)
    )
    max_age: Optional[int] = None
    should_transfer: bool = False
