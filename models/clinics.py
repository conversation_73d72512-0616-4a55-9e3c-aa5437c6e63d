from enum import Enum
from typing import List, Optional, Tuple

from pydantic import BaseModel

from models.motives import Motive
from models.specialities import Speciality


class ClinicType(Enum):
    DEFAULT = "default"
    IMAGING = "imaging"
    ANESTHESIA = "anesthesia"
    DENTIST = "dentist"


class Agenda(BaseModel):
    id: int
    practitioner_id: Optional[int] = None
    name: str
    practice_id: Optional[str] = None

    # Agenda / Doctor restrictions
    min_age: Optional[int] = None
    max_age: Optional[int] = None
    should_transfer: bool = False


class DelayStrategy(Enum):
    ADD_NOTE = "ADD_NOTE"
    CREATE_TASK = "CREATE_TASK"
    FORWARD_CALL = "FORWARD_CALL"


class ForwardStrategy(Enum):
    NEVER = "NEVER"
    ONLY_EMERGENCY = "ONLY_EMERGENCY"
    LIMITED = "LIMITED"
    ALWAYS = "ALWAYS"


class OpeningHour(BaseModel):
    day: int
    ranges: List[Tuple[str, str]]
    enabled: bool


class BookingProviderType(Enum):
    DOCTOLIB = "DOCTOLIB"
    MAIIA = "MAIIA"
    NEHS = "NEHS"
    SOFTWAY = "SOFTWAY"
    EDL = "EDL"


class ClinicConfig(BaseModel):
    # Practical infos
    config_id: str
    config_name: str
    practice_id: str  # TODO: Should we rename clinic -> practice ? This field should be called `id` anyway as it represents the model ID
    clinic_type: ClinicType = ClinicType.DEFAULT
    booking_provider_type: Optional[BookingProviderType] = None
    name: str
    address: str
    openings: List[OpeningHour]
    additional_information: str
    forward_number: Optional[str]

    # Booking config
    motives: List[Motive]
    agendas: List[Agenda]
    specialities: List[Speciality] = []

    # Clinic config
    pre_roll_audio_url: Optional[str] = None
    say_doctor_name: bool = False
    select_doctor: bool = False
    select_clinic: bool = False
    min_age: Optional[int] = None
    max_age: Optional[int] = None
    confirmed_booking_instructions: Optional[str] = None
    delay_strategy: DelayStrategy
    forward_strategy: ForwardStrategy
    emergency_prompt: Optional[str] = None
    motive_collection_prompt: Optional[str] = None
    words_boost: List[str] = []

    experiments: List[str] = []
    is_demo: bool
