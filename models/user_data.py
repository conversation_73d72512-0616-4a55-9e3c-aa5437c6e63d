from enum import Enum, unique
from typing import TYPE_CHECKING, Any, Dict, List, Optional

from livekit.agents import BackgroundAudioPlayer, JobContext, RunContext
from pydantic import BaseModel, Field

from models.appointments import Appointment
from models.availabilities import Availability
from models.calls import CallConfig
from models.clinics import Agenda, ClinicConfig
from models.motives import Motive
from models.patients import Patient, PatientIdentity

if TYPE_CHECKING:
    from agent.base_agent import BaseAgent
else:
    BaseAgent = Any


@unique
class CallIntent(Enum):
    BOOKING = "booking"
    RESCHEDULE = "reschedule"
    CANCEL = "cancel"
    DELAY = "delay"
    CONFIRM = "confirm"
    QUESTIONS = "questions"


class UserData(BaseModel):
    # Call config
    config_id: str
    call_config: CallConfig
    clinic_config: ClinicConfig
    company_clinics: List[ClinicConfig]

    # Call state
    ## Call Intent (Booking, Reschedule, Cancel, Delay, Question, etc)
    intent: CallIntent = CallIntent.QUESTIONS
    ## Current Patient data
    ### Patient base identity (for newcomers)
    patient_identity: Optional[PatientIdentity] = None
    ### Current patient from retrieved identities
    patient: Optional[Patient] = None
    ### All retrieved patients from phone number lookup
    retrieved_patients: List[Patient] = Field(default_factory=list)
    ## Current appointment motive
    motive: Optional[Motive] = None
    secondary_motive: Optional[Motive] = None  # Used for imaging
    ## Current doctor (if any)
    doctor: Optional[Agenda] = None
    ## Specific clinic (if any)
    practice_id: Optional[str] = None
    ## Current availabilities for a given motive
    availabilities: List[Availability] = []
    ## Current select appointment slot
    appointment_slot: Optional[Availability] = None
    ## Current appointment (in-booking or to reschedule/cancel)
    appointment: Optional[Appointment] = None

    # End call state
    notes: List[str] = Field(default_factory=list)
    is_call_forwarded: bool = False
    is_call_successful: bool = False

    # Bot config
    agents: Dict[str, BaseAgent] = Field(default_factory=dict)
    audio_player: BackgroundAudioPlayer
    ctx: JobContext

    class Config:
        arbitrary_types_allowed = True


RunContext_T = RunContext[UserData]
