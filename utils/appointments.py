from datetime import datetime, timezone
from typing import Any, List, Optional

import sentry_sdk

from models.appointments import Appointment
from models.clinics import ClinicConfig
from models.errors import ErrorType
from models.motives import Motive
from models.user_data import UserData


def format_appointments(
    clinic_config: ClinicConfig, appointments: List[Appointment]
) -> str:
    """
    Format appointments with linked appointments on the same line so the AI can easily act on those appointments jointly.
    """
    linked_appointment_ids = [
        a.linked_appointment_id for a in appointments if a.linked_appointment_id
    ]

    linked_appointments_by_id = {
        a.id: a for a in appointments if a.id in linked_appointment_ids
    }

    only_master_appointments = [
        a for a in appointments if a.id not in linked_appointment_ids
    ]

    appointment_texts = []
    for appointment in only_master_appointments:
        base_text = f"{appointment}"

        found_doctor = next(
            (
                a
                for a in clinic_config.agendas
                if a.id == appointment.agenda_id and a.practitioner_id
            ),
            None,
        )
        if found_doctor and clinic_config.say_doctor_name:
            base_text += f" with Doctor {found_doctor.name}"

        if appointment.linked_appointment_id:
            linked_appointment = linked_appointments_by_id[
                appointment.linked_appointment_id
            ]
            base_text += f" followed by {linked_appointment.motive.name}"
        appointment_texts.append(base_text)

    return "\n".join([f"- {t}" for t in appointment_texts])


def build_appointment(  # type: ignore
    patient_id: str, raw_appointment: Any, motives: List[Motive]
) -> Optional[Appointment]:
    motive_id = raw_appointment.get("visit_motive_id")
    motive = next((m for m in motives if m.id == motive_id), None)

    if not motive:
        sentry_sdk.set_tag("patient_id", patient_id)
        sentry_sdk.set_context("raw_appointment", raw_appointment)
        sentry_sdk.set_tag("error_type", ErrorType.DATA_CONSISTENCY.value)
        sentry_sdk.capture_message(
            "A patient's appointment motive is missing in configuration"
        )
        return None

    return Appointment(
        id=raw_appointment.get("id"),
        patient_id=patient_id,
        agenda_id=raw_appointment.get("agenda_id"),
        start_date=raw_appointment.get("start_date"),
        end_date=raw_appointment.get("end_date"),
        motive=motive,
    )


def build_appointments(  # type: ignore
    patient_id: str, raw_appointments: List[Any], motives: List[Motive]
) -> List[Appointment]:
    appointments = []
    for raw_appointment in raw_appointments:
        appointment = build_appointment(patient_id, raw_appointment, motives)
        if not appointment:
            continue

        # Find linked appointment from raw appointment steps
        steps = raw_appointment.get("steps", [])
        if len(steps) > 0:
            linked_appointment_step = next(
                (s for s in steps if s.get("visit_motive_id") != appointment.motive.id),
                None,
            )
            if linked_appointment_step:
                linked_raw_appointment = next(
                    (
                        a
                        for a in raw_appointments
                        if a.get("visit_motive_id")
                        == linked_appointment_step.get("visit_motive_id")
                        and datetime.fromisoformat(a.get("start_date")).astimezone(
                            timezone.utc
                        )
                        == datetime.fromisoformat(
                            linked_appointment_step.get("start_date")
                        ).astimezone(
                            timezone.utc
                        )  # TODO: Let's be careful with dates inconsistently formatted
                    ),
                    None,
                )
                if linked_raw_appointment:
                    appointment.linked_appointment_id = linked_raw_appointment.get("id")
                else:
                    sentry_sdk.set_context("raw_appointment", raw_appointment)
                    sentry_sdk.set_context(
                        "linked_appointment_step", linked_appointment_step
                    )
                    sentry_sdk.capture_message("Could not find linked appointment")
            else:
                sentry_sdk.set_context("raw_appointment", raw_appointment)
                sentry_sdk.capture_message("Could not find linked appointment")

        appointments.append(appointment)

    return appointments


def build_demo_appointment(userdata: UserData) -> Appointment:
    if not userdata.motive or userdata.appointment_slot is None:
        raise ValueError("Missing appointment data for demo booking/rescheduling")

    return Appointment(
        id="demo",
        patient_id="demo",
        agenda_id=userdata.appointment_slot.agenda_id,
        start_date=userdata.appointment_slot.start_date,
        end_date=userdata.appointment_slot.end_date,
        motive=userdata.motive,
    )
