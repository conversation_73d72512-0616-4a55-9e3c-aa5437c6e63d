from datetime import datetime
from typing import List

from models.clinics import ClinicConfig
from models.patients import Patient


from pathlib import Path
from typing import Any, Dict

from jinja2 import Environment, FileSystemLoader


def _render_template(template_name: str, context: Dict[str, Any]) -> str:
    templates_dir = Path(__file__).parent.parent / "prompts"
    env = Environment(
        loader=FileSystemLoader(templates_dir),
        trim_blocks=True,
        lstrip_blocks=True,
    )
    template = env.get_template(template_name)
    return template.render(**context)


def build_global_system_prompt(clinic_config: ClinicConfig) -> str:
    return _render_template(
        "global_system.j2",
        {"clinic_config": clinic_config.model_dump(), "current_date": datetime.now()},
    )


def build_greeting_prompt(clinic_name: str) -> str:
    return _render_template("greeting.j2", {"clinic_name": clinic_name})


def build_name_prompt(
    retrieved_patients: List[Patient], check_patient_is_known: bool = True
) -> str:
    return _render_template(
        "name_collection.j2",
        {
            "retrieved_patients": [
                p.model_dump(mode="json") for p in retrieved_patients
            ],
            "check_patient_is_known": check_patient_is_known,
        },
    )


def build_no_forward_prompt(clinic_config: ClinicConfig) -> str:
    return _render_template(
        "no_forward.j2", {"clinic_config": clinic_config.model_dump()}
    )


END_OF_FLOW_PROMPT = """
    Ask the patient if you can help them with anything else.
    If not, thank them for calling and end the conversation using `end_call`.
"""

IMAGING_FLOW_BOOKING_PROMPT = """
    ### Rules for imaging motives:
    For some specific motives, **before confirming motive**, you must ask additional questions before proceeding to the next step. Ask questions one by one and wait for the patient to answer before asking the next question.
    - **MRI**: For MRI motives, ask (only once) the patient if they have any metal in their body (implants, pacemakers, etc.), or if they are pregnant
        - No need to ask the question twice if the patient already answered previously
        - Example in French: "Avant de programmer votre IRM, merci de me dire si vous avez un pacemaker, une valve cardiaque ou type d'objet métallique dans le corps, pour les femmes, si vous êtes enceinte. Est-ce que l'un de ces cas vous concerne ?"
        - If the patient answers yes or doesn't know or seems hesitant to any of the questions, tell them that for security reason you are not allowed to proceed with the booking but you will leave a note to the reception and end the call.
    - **Open/Closed field MRI**: Some motive MRI exams can be booked in open-field for claustrophobic patients. It allows someone to be present with the patient to take their hand for instance. However, not all MRI motives can be booked in open-field.
        - When similar motives exist with and without open-field, ask the patient which they prefer
        - Example in French: "Souhaitez-vous une IRM à champ ouvert ?"
        - If the patient answers yes, select the motive with open-field, else prefer the closed-field MRI by default.
    - **Injection**: Some motives can be booked with or without a contrast agent injection. 
        - Make sure to ask the patient if they want an injection.
            - Example in French: "Un produit de contraste par injection est-il prescrit sur votre ordonnance ?"
            - If the patient answers yes, select the motive with injection
        - If the motive requires an injection, ask the patient if they have any allergies to contrast agents
            - Example in French: "Avez-vous une allergie au produit de contraste ?"
            - If the patient answers yes, doesn't know or seems hesitant, tell them that for security reason you are not allowed to proceed with the booking but you will leave a note to the reception and end the call.
        - If the motive requires an injection, also ask the patient if they have diabetes. 
            - Example in French: « Etes-vous diabétique ? » 
            - IF yes then create a note as well
    - **Scanner and IRM for >60yo patients**: If a patient older than 60 wants to book a scanner or IRM with injection, we must check if they have a creatinine blood test result less than 3 months old. 
        - Example in French: "Avez-vous bien un bilan sanguin datant de moins de 3 mois avec votre taux de créatinine?" 
        - If they do, continue the flow
        - If they don't, tell them you will leave a note to the reception and end the call
    - **Screening vs Prescribed Mammography**: Usually, clinics propose two types of mammography: screening mammography (100% coverage by insurance) and diagnostic mammography (not always covered by insurance).
        - Ask the patient if they were prescribed by a doctor or if they come from a screening campaign.
        - From their answer, select the most appropriate motive.

    ### Step 2.1 Double motive
    Once the first motive is confirmed, you must always ask the patient if they need a second exam. 
    Repeat step 2 to collect secondary motive and use `confirm_motive` tool again with `is_secondary_motive` set to `True`. 
    - If a patient comes with a single prescription with two different exams, this is considered a single appointment and does not fall under the "multi-appointments booking" rule.
"""

IMAGING_END_OF_FLOW_INSTRUCTIONS = "Tell the patient they must check their Doctolib account to review the possible pre-exam prescriptions and instructions."
