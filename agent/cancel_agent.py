from typing import Annotated, List

from livekit.agents import function_tool
from loguru import logger
from pydantic import Field

from agent.base_agent import BaseAgent
from agent.tools.confirm_appointment_selection import (
    confirm_appointment_selection
)
from agent.tools.confirm_identity import confirm_identity
from models.clinics import ClinicConfig
from models.patients import Patient
from models.user_data import RunContext_T
from services.appointments import cancel_appointment
from utils.prompts import (
    END_OF_FLOW_PROMPT,
    build_global_system_prompt,
    build_name_prompt
)


def build_system_prompt(
    clinic_config: ClinicConfig, retrieved_patients: List[Patient]
) -> str:
    return f"""
        {build_global_system_prompt(clinic_config)}
        
        
        # Instructions:
       
        Your goal is to **guide the user through cancelling an existing medical appointment** from start to finish, following the steps below. 
        - Begin the flow immediately (do not introduce yourself with extra chit-chat) and lead the patient through each step, while adhering to the above guidelines. 
        - The appointment cancellation is only successful when you have called the `confirm_cancellation` tool with all required details and the user has confirmed everything.
            - If the tool is not called, the cancellation is not considered successful, try to figure out what is blocking you and try to resolve it
            
        ### 1. Patient identity collection
        {build_name_prompt(retrieved_patients, False)}
        
        ### 2. Appointment selection
        Once the patient is identified, you should see the current pending appointments they have.
        - If the patient has only one appointment, ask them to confirm that it is the one they want to cancel.
        - If the patient has no appointment, stop the flow and inform them that there is no appointment to cancel.
        - If the patient has multiple appointments, ask them to confirm the one they want to cancel.
        - Once the appointment to cancel is confirmed, call the `confirm_appointment_selection` tool with the appointment ID.
        
        ### 3. Reason for cancellation
        Ask the patient for the reason of the cancellation. They must provide an answer before proceeding to the next step.
        
        ### 4. Cancellation confirmation
        Once the patient has selected the appointment they want to cancel, and gaven a reason for the cancellation, confirm the cancellation with them.
            - Example: "Je vais procéder à l'annulation de votre rendez-vous pour un bilan sanguin le 1er janvier à 10h, en notant que la raison est 'Je suis en deplacement'. Est-ce correct ?"
            - If the patient confirms, call the `confirm_cancellation` tool with the reason for the cancellation.
            - If the patient declines, ask them how they want to proceed.
        Once the cancellation is confirmed, inform the patient that the cancellation is successful.
        
        ### 5. End of flow
        {END_OF_FLOW_PROMPT}
    """


class CancelAgent(BaseAgent):
    name = "cancel"

    def __init__(self, clinic_config: ClinicConfig, retrieved_patients: List[Patient]):
        super().__init__(
            clinic_config=clinic_config,
            instructions=build_system_prompt(clinic_config, retrieved_patients),
            tools=[confirm_identity, confirm_appointment_selection],
        )

    @function_tool()
    async def confirm_cancellation(
        self,
        context: RunContext_T,
        reason: Annotated[
            str,
            Field(description="The reason for the cancellation"),
        ],
    ) -> str:
        """
        Called when the user confirms the appointment they want to cancel and gave a cancellation reason.
        """
        if not context.userdata.appointment:
            return "No appointment selected for cancellation. Please select an appointment before confirming cancellation."

        logger.info(
            f"Confirming cancellation of appointment {context.userdata.appointment.id} for {reason}"
        )

        await cancel_appointment(
            context.userdata.clinic_config,
            context.userdata.appointment.id,
            f"Annulation: {reason}",
        )
        context.userdata.is_call_successful = True
        await context.userdata.audio_player.play("./assets/ding.wav")
        return "Appointment cancelled."
