import asyncio
from typing import Ann<PERSON><PERSON>, <PERSON><PERSON>, Union

from livekit.agents import Agent, function_tool
from livekit.rtc import SipDTM<PERSON>
from loguru import logger
from pydantic import Field

from agent.base_agent import BaseAgent
from agent.welcome_agent import WelcomeAgent
from models.clinics import ClinicConfig
from models.user_data import RunContext_T
from services.user_data import prepare_user_data
from utils.phone_numbers import format_phone_number, is_valid_french_mobile_phone_number
from utils.prompts import build_global_system_prompt, build_greeting_prompt


def build_system_prompt(clinic_config: ClinicConfig) -> str:
    return f"""
        {build_global_system_prompt(clinic_config)}
    
        Your job is to greet the patient and collect their **mobile** phone number before going further:
        1. {build_greeting_prompt(clinic_config.name)}
        2. Ask them politely that you need them to input their mobile phone number and terminate with a # before going further
            - **Example in French**: "Afin de poursuivre, merci de composer votre numéro de portable suivi de la touche dièse."
        3. If and only if the patient is unable to type their mobile phone number (for instance because they don't have a keypad), ask them to spell it out number by number. 
            - Once they have spelled out the mobile phone number, call the `validate_phone_number` tool 
        4. After 2 unsuccessful attempts or if the patient gets angry, you can skip the phone number validation by calling `skip_phone_number_validation`
    """


class PhoneCollectorAgent(BaseAgent):
    name = "phone_collector"

    def __init__(self, clinic_config: ClinicConfig) -> None:
        super().__init__(
            clinic_config=clinic_config, instructions=build_system_prompt(clinic_config)
        )
        self.phone_number = ""
        self.attempts_count = 0

    async def on_enter(self) -> None:
        self.session.userdata.ctx.room.on(
            "sip_dtmf_received", self.on_sip_dtmf_received
        )
        await super().on_enter()

    def on_sip_dtmf_received(self, dtmf_event: SipDTMF) -> None:
        asyncio.create_task(self._handle_dtmf(dtmf_event))

    async def _handle_dtmf(self, dtmf_event: SipDTMF) -> None:
        # Update user state so the idle callback doesn't get triggered
        self.session._update_user_state("speaking")

        digit = dtmf_event.digit
        logger.info(f"Received DTMF: {digit}")

        if digit not in ["#", "*"]:
            self.phone_number += digit
        else:  # Termination digit
            logger.info(f"Final phone number: {self.phone_number}")
            await self.session.generate_reply(
                instructions=f"Call the `validate_phone_number` tool with {self.phone_number}"
            )

    def _transfer_to_welcome(
        self, context: RunContext_T, additional_instructions: str
    ) -> Tuple[Agent, str]:
        welcome_agent = WelcomeAgent(context.userdata.clinic_config)
        welcome_agent.mute_on_enter = True
        return (
            welcome_agent,
            f"{additional_instructions}. Don't greet the patient, just ask them how you can help",
        )

    @function_tool()
    async def skip_phone_number_validation(
        self,
        context: RunContext_T,
    ) -> Tuple[Agent, str]:
        """
        Called when the user has failed to spell their phone number after 2 attempts
        """
        logger.warning("Skipping phone number validation")
        return self._transfer_to_welcome(
            context,
            "Tell the patient you couldn't validate the phone number and you'll skip the phone number validation",
        )

    @function_tool()
    async def validate_phone_number(
        self,
        context: RunContext_T,
        phone_number: Annotated[
            str,
            Field(description="The phone number to validate."),
        ],
    ) -> Union[str, Tuple[Agent, str]]:
        """
        Called when the user has entered their phone number and terminated with a #
        """
        self.attempts_count += 1
        logger.info(
            f"Validating phone number: {phone_number} (attempt #{self.attempts_count})"
        )

        if not is_valid_french_mobile_phone_number(phone_number):
            logger.warning(f"Invalid phone number: {phone_number}")
            self.phone_number = ""
            if self.attempts_count >= 2:
                # After 2 attempts, we just skip the phone number validation and go to the next step
                return self.skip_phone_number_validation(context)  # type: ignore
            return "The number is incorrect. It should be a valid french mobile number, starting with 06 or 07 and containing 10 digits. Ask the patient to try again"

        formatted_phone_number = format_phone_number(phone_number)
        logger.info(f"Phone number validated: {formatted_phone_number}")

        context.userdata.ctx.room.off("sip_dtmf_received", self._handle_dtmf)
        # TODO: should we replace the caller number or should we store it somewhere else to keep both information?
        #  Problem to solve: report calls needs a phone number to identify or call the patient.
        context.userdata.call_config.caller_phone_number = formatted_phone_number

        # TODO: couldn't find a better way to refresh the user data, so we can use retrieved identites in all agents
        #  Problem to solve: only augment context without breaking the identity flow (currently the bot doesn't ask for retrieved identities and acts as if no profile were found)
        context.session.userdata = await prepare_user_data(
            context.userdata.call_config, context.userdata.ctx
        )

        return self._transfer_to_welcome(
            context,
            "Thank the patient and tell them their phone number has been registered",
        )
