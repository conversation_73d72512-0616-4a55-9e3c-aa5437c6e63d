from collections import defaultdict
from datetime import date, datetime
from typing import Annotated, List, Optional

from livekit.agents import function_tool
from loguru import logger
from pydantic import Field

from models.availabilities import Availability
from models.user_data import RunContext_T
from services.availabilities import fetch_next_availabilities
from utils.strings import remove_text_in_parentheses


def format_availability(
    availability: Availability,
) -> str:
    current_year = date.today().year
    # Only say year if not current year
    availability_format = (
        "%A %d %B at %H:%M"
        if availability.start_date.year == current_year
        else "%A %d %B %Y at %H:%M"
    )

    is_substitute = (
        f" (being replaced with {remove_text_in_parentheses(availability.substitute_doctor_name)})"
        if availability.substitute_doctor_name
        else ""
    )
    practice_id = (
        f"at practice_id: {availability.practice_id}"
        if availability.practice_id
        else ""
    )
    return f"{availability.start_date.strftime(availability_format)} {is_substitute} {practice_id} (ends at {availability.end_date.strftime('%H:%M')})"


def format_availabilities(
    proposed_date: Optional[date],
    availabilities: List[Availability],
    sort_by_doctor: bool,
) -> str:
    response_lines = [
        (
            f"Availabilities found for {proposed_date}:"
            if proposed_date
            else "Closest availabilities:"
        )
    ]

    if not sort_by_doctor:
        return "\n".join(
            response_lines + [f"  - {format_availability(a)}" for a in availabilities]
        )

    availabilities_by_doctor = defaultdict(list)
    for availability in availabilities:
        availabilities_by_doctor[availability.doctor_name].append(availability)

    for doctor_name, doctor_availabilities in availabilities_by_doctor.items():
        response_lines.append(
            f"  - With Doctor {remove_text_in_parentheses(doctor_name)}"
            if doctor_name
            else "  - With no specific doctor"
        )
        for availability in doctor_availabilities:
            response_lines.append(f"    - {format_availability(availability)}")

    return "\n".join(response_lines)


@function_tool()
async def find_availabilities(
    context: RunContext_T,
    proposed_date: Annotated[
        Optional[str],
        Field(
            description="The date for which to retrieve availabilities, format is YYYY-MM-DD"
        ),
    ] = None,
    with_substitute: Annotated[
        bool,
        Field(
            description="If true, will search for availabilities with possible substitute doctors",
        ),
    ] = True,
) -> str:
    """
    Called to retrieve availabilities for the patient visit.
    """
    logger.info(f"Finding availabilities for {proposed_date}")
    if not context.userdata.motive:
        return "Appointment motive is not confirmed. Collect it before finding availabilities."

    parsed_date = (
        date.today()
        if not proposed_date
        else datetime.strptime(proposed_date, "%Y-%m-%d").date()
    )

    availabilities = await fetch_next_availabilities(
        context.userdata.clinic_config,
        context.userdata.motive,
        context.userdata.secondary_motive,
        context.userdata.practice_id,
        context.userdata.doctor.id if context.userdata.doctor else None,
        parsed_date,
        with_substitute,
    )

    # Call is considered successful if we reach the availabilities step
    context.userdata.is_call_successful = True

    if not len(availabilities):
        if not context.userdata.doctor:
            return "No availabilities found for this motive. Propose to select another motive or leave a note to the reception."
        return "No availabilities found for this doctor. Propose to select another doctor or leave a note to the reception."

    context.userdata.availabilities += availabilities

    return format_availabilities(
        parsed_date, availabilities, context.userdata.clinic_config.select_doctor
    )
