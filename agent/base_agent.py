import asyncio
from typing import TYPE_CHECKING, Annotated, AsyncIterable, Op<PERSON>, Tu<PERSON>, Union

import sentry_sdk

from models.errors import ErrorType
from utils.prompts import build_no_forward_prompt

if TYPE_CHECKING:
    pass

from livekit.agents import Agent, ModelSettings, function_tool
from livekit.agents.voice.transcription.filters import filter_markdown
from loguru import logger
from pydantic import Field

from models.clinics import ClinicConfig, ForwardStrategy
from models.user_data import CallIntent, RunContext_T
from services.telephony import forward_call
from utils.clinics import is_clinic_open, is_motive_available_at_clinic


class BaseAgent(Agent):
    name = "base"

    def __init__(  # type: ignore[no-untyped-def]
        self, clinic_config: ClinicConfig, instructions: str, *args, **kwargs
    ) -> None:
        self.mute_on_enter = False  # TODO: is it really necessary?
        self.clinic_config = clinic_config

        super().__init__(
            instructions=instructions,
            *args,
            **kwargs,
        )

    async def on_enter(self) -> None:
        logger.info(
            f"Starting {self.__class__.__name__} workflow (muted: {self.mute_on_enter})"
        )
        # Give the agent the whole session context
        await self.update_chat_ctx(self.session.history)
        if not self.mute_on_enter:
            await self.session.generate_reply()

    def tts_node(self, text: AsyncIterable[str], model_settings: ModelSettings):  # type: ignore[no-untyped-def]
        filtered_text = filter_markdown(text)
        return super().tts_node(filtered_text, model_settings)

    @function_tool()
    async def forward_call(
        self,
        context: RunContext_T,
        forward_number: Annotated[
            Optional[str], Field(description="The number to forward the call to")
        ],
    ) -> Optional[str]:
        """
        Called whenever the patient wants to be forwarded to a human agent
        """
        number = forward_number or context.userdata.clinic_config.forward_number
        logger.info(f"Forwarding call to {number}")

        if not is_clinic_open(context.userdata.clinic_config.openings):
            logger.info("Clinic is closed, cannot forward, leaving a note.")
            return "Tell the patient that the clinic is closed and that you will leave a note to the reception."

        if (
            not number
            or context.userdata.clinic_config.forward_strategy == ForwardStrategy.NEVER
        ):
            logger.warning("No forward number or cannot forward, leaving a note.")
            return build_no_forward_prompt(context.userdata.clinic_config)

        await self.session.generate_reply(
            instructions="Explain that you are now going to transfer the patient to the reception and that the call will be picked up shortly."
        )
        await asyncio.sleep(3)

        try:
            await forward_call(
                room=context.userdata.ctx.room,
                forward_number=number,
            )
            context.userdata.is_call_forwarded = True
            context.userdata.is_call_successful = True
            return None
        except Exception as e:
            sentry_sdk.set_tag("error_type", ErrorType.FORWARD_CALL.value)
            sentry_sdk.capture_exception(e)
            logger.error(f"Failed to forward call: {e}")
            return "Tell the patient you couldn't forward the call to the reception, possibly because all lines are busy. Leave a note to the reception and end the call."

    @function_tool()
    async def leave_note(
        self,
        context: RunContext_T,
        note: Annotated[
            str,
            Field(
                description="The note description. It must be a short and concise description of what task the reception should follow up on."
            ),
        ],
    ) -> str:
        """
        Called whenever you need to leave a note for the reception to follow up on or the patient asks you to.
        """
        logger.info(f"Adding note: {note}")
        context.userdata.notes.append(note)
        context.userdata.is_call_successful = True

        if not context.userdata.patient_identity:
            logger.info("Confirming patient identity before leaving a note")
            return "Patient identity is not confirmed. Collect it, then call `confirm_identity`, then `leave_note` again."
        return "Resume the conversation. If the patient is stuck, ask them how you can help them. If the patient has no more requests, you can end the call."

    # TODO: should it be a tool? Or a simple handler, call programmatically
    @function_tool()
    async def end_call(
        self,
        context: RunContext_T,
    ) -> Union[str, Tuple[Agent, str]]:
        """
        Called whenever the call should end.
        The user can never trigger this tool on their own.
        It must be called only when we reached a step where the call should end.
        """
        logger.info("Ending call")

        questions_agent = await self.change_intent(context, CallIntent.QUESTIONS)
        # TODO: Made to avoid double repetitions when ending the call. I'm not exactly sure this is the right correct fix
        if not isinstance(questions_agent, str):
            questions_agent.mute_on_enter = True
            # TODO: probably should let a few tools there
            # FIXME: an empty list make the agent crash
            await questions_agent.update_tools([self.end_call])
        return (
            questions_agent,
            """
                End the call on a polite, reassuring note. For instance, "*Merci de votre confiance, **à bientôt** et bonne journée !*".
                You will now only be able to answer questions about the clinic and the medical practice.
                If the patient wants to do another action like booking a new appointment or cancel an existing one, politely ask them to make a new call.
            """,
        )

    @function_tool()
    async def change_intent(
        self,
        context: RunContext_T,
        intent: CallIntent,
    ) -> Union[Agent, str]:
        """
        Called when the patient manifested a new intent during their call.
        Patient can change intent at any time during the call.

        Args:
            intent (enum): The new intent of the patient.
            Possible values are:
            - "booking": for booking an appointment
            - "reschedule": for rescheduling an appointment
            - "cancel": for cancelling an appointment
            - "delay": for notifying the clinic that the patient will be late to their appointment
            - "confirm": for confirming that the patient will attend their appointment
            - "questions": for answering questions about the clinic and the medical practice
        """
        logger.info(f"Change intent to to {intent}")

        if intent.value == self.name:
            logger.info(f"Agent {intent} is already active, skipping transfer")
            return "Keep going"

        next_agent = context.userdata.agents[intent.value]

        if intent != CallIntent.QUESTIONS:
            context.userdata.intent = intent
            logger.info(f"Setting intent to {context.userdata.intent}")

        return next_agent

    # TODO: this should not live in base agent
    #  Problem: should_leave_note might require practice_id to be set
    @function_tool()
    async def confirm_clinic_selection(
        self,
        context: RunContext_T,
        practice_id: Annotated[
            Optional[str],
            Field(
                description="The practice ID the patient wants to interact with. None if the patient doesn't want a specific practice."
            ),
        ],
    ) -> Optional[str]:
        """
        Called when the user confirms a specific clinic they want to interact with
        """
        logger.info(f"Confirming clinic selection: {practice_id}")  # noqa: F821

        if practice_id is None:
            config_global = next(
                (
                    clinic
                    for clinic in context.userdata.company_clinics
                    if clinic.practice_id == "global"
                ),
                context.userdata.clinic_config,
            )
            context.userdata.config_id = config_global.config_id
            context.userdata.practice_id = None
            return "Now let's go to next step"

        found_practice = next(
            (
                clinic
                for clinic in context.userdata.company_clinics
                if clinic.practice_id == practice_id
            ),
            None,
        )

        if not found_practice:
            return f"Invalid practice ID: {practice_id}. Please try again."

        logger.info(f"Patient selected clinic: {found_practice.name}")  # noqa: F821
        context.userdata.practice_id = found_practice.practice_id
        context.userdata.config_id = found_practice.config_id

        if not context.userdata.patient_identity:
            return "Patient identity is not confirmed. Collect it again before finding availabilities."

        if (
            context.userdata.clinic_config.select_clinic
            and context.userdata.motive
            and not is_motive_available_at_clinic(
                context.userdata.motive, found_practice.practice_id
            )
        ):
            return f"This motive is not available at the clinic where the patient wants to book. Propose the patient to select another motive or to select another clinic. (motive practice_ids: {context.userdata.motive.practice_ids}, selected clinic: {found_practice.practice_id})"

        return "Now let's go to next step"
