from typing import List

from agent.base_agent import BaseAgent
from agent.tools.confirm_appointment_selection import (
    confirm_appointment_selection
)
from agent.tools.confirm_identity import confirm_identity
from models.clinics import ClinicConfig
from models.patients import Patient
from utils.prompts import (
    END_OF_FLOW_PROMPT,
    build_global_system_prompt,
    build_name_prompt
)


def build_system_prompt(
    clinic_config: ClinicConfig, retrieved_patients: List[Patient]
) -> str:
    return f"""
        {build_global_system_prompt(clinic_config)}
        
        
        # Instructions:

        Your goal is to **guide the user through confirming an existing medical appointment** from start to finish, following the steps below.
        - Begin the flow immediately (do not introduce yourself with extra chit-chat) and lead the patient through each step, while adhering to the above guidelines.
        - The appointment confirmation is only successful when you have called the `confirm_appointment_selection` tool with all required details and the user has confirmed everything.
            - If the tool is not called, the confirmation is not considered successful, try to figure out what is blocking you and try to resolve it

        ### 1. Patient identity collection
        {build_name_prompt(retrieved_patients, False)}
        
        ### 2. Appointment selection
        Once the patient is identified, you should see the current pending appointments they have.
        - If the patient has only one appointment, ask them to confirm that it is the one they want to confirm.
        - If the patient has no appointment, stop the flow and inform them that there is no appointment to confirm.
        - If the patient has multiple appointments, ask them to confirm the one they want to confirm.
        - Once the appointment to confirm is selected, call the `confirm_appointment_selection` tool with the appointment ID.

        ### 3. End of flow
        {END_OF_FLOW_PROMPT}
    """


class ConfirmAgent(BaseAgent):
    name = "confirm"

    def __init__(self, clinic_config: ClinicConfig, retrieved_patients: List[Patient]):
        super().__init__(
            clinic_config=clinic_config,
            instructions=build_system_prompt(clinic_config, retrieved_patients),
            tools=[confirm_identity, confirm_appointment_selection],
        )
